'use client';

import React, { useState, useEffect, useRef } from 'react';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { 
  ArrowLeft, Send, AlertCircle, Clock, CheckCircle, 
  Paperclip as PaperclipIcon, User, Search, Download,
  FileText, Image as ImageIcon, X
} from 'lucide-react';
import { SupportChat } from './SupportChatList';
import { useAdminWebSocketChat } from '@/hooks/useAdminWebSocketChat';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { UserDetailSidebar } from './UserDetailSidebar';
import { TypingIndicator } from '@/components/ui/typing-indicator';
import { FileUploadProgress } from '@/components/ui/file-upload-progress';
import { WebSocketIndicator } from '@/components/ui/websocket-indicator';
import { MessageStatusIcon } from '@/components/ui/message-status-icon';
import ImageLightbox from '@/components/ui/image-lightbox';
import EmojiPicker from '@/components/ui/emoji-picker';
import { useChatData } from '@/components/context/ChatDataContext';
import toast from 'react-hot-toast';
import { motion, AnimatePresence } from 'framer-motion';
import '@/app/dashboard/chat/chat-styles.css';



interface EnhancedChatDetailViewProps {
  chat: SupportChat;
  onBack: () => void;
  onUpdateStatus: (status: 'pending' | 'in_progress' | 'resolved') => void;
  isModal?: boolean;
}

export const EnhancedChatDetailView: React.FC<EnhancedChatDetailViewProps> = ({
  chat, 
  onBack, 
  onUpdateStatus,
  isModal = false
}) => {
  // Use ChatDataContext for centralized message management
  const { 
    messages: contextMessages, 
    loadingMessages, 
    fetchMessages, 
    sendMessage: contextSendMessage
  } = useChatData();
    // Get messages for this specific chat
  const chatId = chat.id.toString();
  const isLoading = loadingMessages.get(chatId) || false;

  // WebSocket connection using admin context
  const {
    connectionState,
    messages: wsMessages,
    sendMessage: wsSendMessage,
    sendTypingStart,
    sendTypingStop,
    isTyping: isUserTyping,
    typingUsers,
    unreadCount,
    markAsRead,
    uploadProgress
  } = useAdminWebSocketChat({
    chatId: chat.id.toString(),
    autoConnect: true,
    priority: chat.priority as 'low' | 'medium' | 'high' | 'urgent'
  });

  // Local state
  const [newMessage, setNewMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [showUserDetails, setShowUserDetails] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [searchResults, setSearchResults] = useState<number[]>([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(-1);
  const [isDraggingFile, setIsDraggingFile] = useState(false);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [lightboxImage, setLightboxImage] = useState<string | null>(null);
  const [attachments, setAttachments] = useState<File[]>([]);
  const [typingTimeout, setTypingTimeout] = useState<NodeJS.Timeout | null>(null);

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const messageInputRef = useRef<HTMLTextAreaElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);  // Combine historical and WebSocket messages
  const allMessages = React.useMemo(() => {
    // Get messages for this specific chat inside useMemo to avoid dependency issues
    const messages = contextMessages.get(chatId) || [];
    
    // Cast the unknown array to the appropriate type
    const typedWsMessages = wsMessages as {
      id: string;
      sender: 'user' | 'support';
      message: string;
      timestamp: string;
      sender_name?: string;
      status?: 'sending' | 'sent' | 'delivered' | 'read';
      attachments?: {
        id: number;
        file_name: string;
        file_url: string;
        content_type: string;
      }[];
    }[];
    
    const wsMessageIds = new Set(typedWsMessages.map(msg => msg.id));
    const uniqueHistoricalMessages = messages.filter(msg => !wsMessageIds.has(msg.id.toString()));
    
    return [...uniqueHistoricalMessages, ...typedWsMessages.map(wsMsg => ({
      id: parseInt(wsMsg.id),
      sender_type: wsMsg.sender === 'user' ? 'user' as const : 'agent' as const,
      content: wsMsg.message,
      timestamp: wsMsg.timestamp,
      sender_name: wsMsg.sender_name || (wsMsg.sender === 'user' ? chat.user_name : 'Support'),
      status: wsMsg.status,
      attachments: wsMsg.attachments
    }))].sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
  }, [contextMessages, wsMessages, chat.user_name, chatId]);

  // Initial data loading
  useEffect(() => {
    fetchMessages(chatId);
    markAsRead(); // Mark as read when opening chat
  }, [fetchMessages, markAsRead, chatId]);

  // Scroll to bottom when messages change
  useEffect(() => {
    const timer = setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
    
    return () => clearTimeout(timer);
  }, [allMessages]);

  // Handle scroll to show/hide scroll to bottom button
  useEffect(() => {
    const handleScroll = () => {
      if (!messagesContainerRef.current) return;
      
      const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
      const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100;
      setShowScrollToBottom(!isNearBottom);
    };
    
    const container = messagesContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, []);

  // Handle typing indicators
  const handleInputChange = (value: string) => {
    setNewMessage(value);
    
    // Clear existing timeout
    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }
    
    // Send typing start if not already typing
    if (value.trim() && !isUserTyping) {
      sendTypingStart();
    }
    
    // Set timeout to stop typing
    const timeout = setTimeout(() => {
      sendTypingStop();
    }, 1000);
    
    setTypingTimeout(timeout);
  };

  // Send message using context method
  const handleSendMessage = async () => {
    if (!newMessage.trim() || isSending) return;
    
    const messageText = newMessage.trim();
    setNewMessage('');
    setIsSending(true);
    
    try {
      // Send via WebSocket for real-time delivery
      wsSendMessage(messageText);
      
      // Also send via context for persistence with optimistic updates
      await contextSendMessage(chatId, messageText, attachments);
      
      // Clear attachments after sending
      setAttachments([]);
      
      toast.success('Message sent');
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
      // Restore message text on error
      setNewMessage(messageText);
    } finally {
      setIsSending(false);
    }
  };

  // Handle Enter key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // File drag and drop handlers
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (chat.status !== 'resolved') {
      setIsDraggingFile(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingFile(false);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingFile(false);
    
    if (chat.status === 'resolved') return;
    
    const droppedFiles = Array.from(e.dataTransfer.files);
    if (droppedFiles.length > 0) {
      setAttachments(prev => [...prev, ...droppedFiles]);
    }
  };

  // File input handler
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || []);
    if (selectedFiles.length > 0) {
      setAttachments(prev => [...prev, ...selectedFiles]);
    }
  };

  // Remove attachment
  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  // Search functionality
  const handleSearch = () => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      setCurrentSearchIndex(-1);
      return;
    }
    
    const results: number[] = [];
    allMessages.forEach((message, index) => {
      if (message.content.toLowerCase().includes(searchQuery.toLowerCase())) {
        results.push(index);
      }
    });
    
    setSearchResults(results);
    setCurrentSearchIndex(results.length > 0 ? 0 : -1);
  };

  // Navigation for search results
  const nextSearchResult = () => {
    if (searchResults.length === 0) return;
    setCurrentSearchIndex(prev => (prev + 1) % searchResults.length);
  };

  const previousSearchResult = () => {
    if (searchResults.length === 0) return;
    setCurrentSearchIndex(prev => prev === 0 ? searchResults.length - 1 : prev - 1);
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return format(date, 'HH:mm');
    } else if (diffInHours < 168) {
      return format(date, 'EEE HH:mm');
    } else {
      return format(date, 'dd/MM/yyyy HH:mm');
    }
  };

  // Get priority styling
  const getPriorityColor = () => {
    switch (chat.priority) {
      case 'urgent': return 'border-red-500 text-red-600';
      case 'high': return 'border-orange-500 text-orange-600';
      case 'medium': return 'border-yellow-500 text-yellow-600';
      case 'low': return 'border-green-500 text-green-600';
      default: return 'border-gray-500 text-gray-600';
    }
  };

  // Get status badge
  const getStatusBadge = () => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock, label: 'Pending' },
      in_progress: { color: 'bg-blue-100 text-blue-800', icon: AlertCircle, label: 'In Progress' },
      resolved: { color: 'bg-green-100 text-green-800', icon: CheckCircle, label: 'Resolved' }
    };
    
    const config = statusConfig[chat.status as keyof typeof statusConfig];
    const Icon = config.icon;
    
    return (
      <Badge className={`${config.color} border-0`}>
        <Icon className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  // Handle emoji selection
  const handleEmojiSelect = (emoji: string) => {
    setNewMessage(prev => prev + emoji);
    messageInputRef.current?.focus();
  };

  return (
    <div 
      className="flex flex-col h-full bg-white"
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-gray-50">
        <div className="flex items-center gap-3">
          {!isModal && (
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="w-4 h-4" />
            </Button>
          )}
          
          <Avatar className="w-10 h-10">
            <AvatarFallback className={`text-white ${chat.priority === 'urgent' ? 'bg-red-500' : 'bg-blue-500'}`}>
              {chat.user_name.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          
          <div>
            <h2 className="font-semibold text-lg">{chat.user_name}</h2>
            <p className="text-sm text-muted-foreground">{chat.user_email}</p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <WebSocketIndicator connectionState={connectionState} />
          
          {unreadCount > 0 && (
            <Badge variant="destructive">
              {unreadCount} unread
            </Badge>
          )}
          
          <div className={`px-3 py-1 rounded-full border ${getPriorityColor()}`}>
            {chat.priority.charAt(0).toUpperCase() + chat.priority.slice(1)} Priority
          </div>
          
          {getStatusBadge()}
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowUserDetails(!showUserDetails)}
          >
            <User className="w-4 h-4" />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsSearchActive(!isSearchActive)}
          >
            <Search className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Search Bar */}
      {isSearchActive && (
        <div className="p-4 border-b bg-gray-50">
          <div className="flex gap-2">
            <Input
              placeholder="Search messages..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
            />
            <Button onClick={handleSearch} size="sm">
              <Search className="w-4 h-4" />
            </Button>
            <Button variant="outline" onClick={() => setIsSearchActive(false)} size="sm">
              <X className="w-4 h-4" />
            </Button>
          </div>
          
          {searchResults.length > 0 && (
            <div className="flex items-center gap-2 mt-2 text-sm text-muted-foreground">
              <span>{currentSearchIndex + 1} of {searchResults.length} results</span>
              <Button variant="ghost" size="sm" onClick={previousSearchResult}>
                ↑
              </Button>
              <Button variant="ghost" size="sm" onClick={nextSearchResult}>
                ↓
              </Button>
            </div>
          )}
        </div>
      )}

      <div className="flex flex-1 overflow-hidden">
        {/* Messages Area */}
        <div className="flex-1 flex flex-col">
          {/* Messages */}
          <div
            ref={messagesContainerRef}
            className="flex-1 overflow-y-auto pt-8 pb-4"
          >
            {isLoading ? (
              <div className="flex justify-center items-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <AnimatePresence>
                {allMessages.map((message, index) => (
                  <motion.div
                    key={`${message.id}-${message.timestamp}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className={`message-container ${
                      message.sender_type === 'agent'
                        ? 'message-container-agent'
                        : 'message-container-user'
                    }`}
                  >
                    <div
                      className={`p-4 ${
                        message.sender_type === 'agent'
                          ? 'message-bubble-agent text-white'
                          : 'message-bubble-user text-[#133157]'
                      } ${
                        searchResults.includes(index) && currentSearchIndex === searchResults.indexOf(index)
                          ? 'ring-2 ring-yellow-400'
                          : ''
                      }`}
                    >
                      <div className="text-sm font-medium mb-1">
                        {message.sender_name}
                      </div>
                      
                      <div className="text-sm whitespace-pre-wrap">
                        {message.content}
                      </div>
                        {message.attachments && message.attachments.length > 0 && (
                        <div className="mt-2 space-y-2">
                          {message.attachments.map((attachment: {
                            id: number;
                            file_name: string;
                            file_url: string;
                            content_type: string;
                          }) => (
                            <div
                              key={attachment.id}
                              className="flex items-center gap-2 p-2 bg-white/10 rounded cursor-pointer hover:bg-white/20"
                              onClick={() => window.open(attachment.file_url, '_blank')}
                            >
                              {attachment.content_type.startsWith('image/') ? (
                                <ImageIcon className="w-4 h-4" />
                              ) : (
                                <FileText className="w-4 h-4" />
                              )}
                              <span className="text-xs truncate">{attachment.file_name}</span>
                              <Download className="w-3 h-3 ml-auto" />
                            </div>
                          ))}
                        </div>
                      )}
                      
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs opacity-70">
                          {formatTimestamp(message.timestamp)}
                        </span>
                        
                        {message.sender_type === 'agent' && message.status && (
                          <MessageStatusIcon status={message.status} />
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            )}            {/* Typing Indicator */}
            {typingUsers.length > 0 && (
              <TypingIndicator typingUsers={typingUsers as {
                user_id: string;
                user_name: string;
                timestamp: Date;
              }[]} />
            )}
            
            <div ref={messagesEndRef} />
          </div>          {/* Upload Progress */}
          {uploadProgress.size > 0 && (
            <div className="p-4 border-t">
              <FileUploadProgress uploads={uploadProgress as Map<string, {
                message_id: string;
                progress: number;
                file_name: string;
                status?: 'uploading' | 'completed' | 'error';
                error_message?: string;
              }>} />
            </div>
          )}

          {/* Message Input */}
          {chat.status !== 'resolved' && (
            <div className="border-t p-4">
              {/* File Attachments Preview */}
              {attachments.length > 0 && (
                <div className="mb-3 flex flex-wrap gap-2">
                  {attachments.map((file, index) => (
                    <div key={index} className="flex items-center gap-2 bg-gray-100 rounded px-3 py-2">
                      <FileText className="w-4 h-4" />
                      <span className="text-sm truncate max-w-[150px]">{file.name}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeAttachment(index)}
                        className="h-6 w-6 p-0"
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
              
              <div className="flex gap-2">
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  className="hidden"
                  onChange={handleFileSelect}
                  accept=".pdf,.jpg,.jpeg,.png,.doc,.docx,.txt"
                />
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isSending}
                >
                  <PaperclipIcon className="w-4 h-4" />
                </Button>
                
                <EmojiPicker onEmojiSelect={handleEmojiSelect} />
                
                <Textarea
                  ref={messageInputRef}
                  placeholder="Type your response..."
                  value={newMessage}
                  onChange={(e) => handleInputChange(e.target.value)}
                  onKeyDown={handleKeyPress}
                  disabled={isSending}
                  className="resize-none"
                  rows={1}
                />
                
                <Button
                  onClick={handleSendMessage}
                  disabled={!newMessage.trim() || isSending}
                  className="px-4"
                >
                  {isSending ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <Send className="w-4 h-4" />
                  )}
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* User Details Sidebar */}
        <AnimatePresence>
          {showUserDetails && (
            <motion.div
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: 300, opacity: 1 }}
              exit={{ width: 0, opacity: 0 }}
              className="border-l overflow-hidden"
            >
              <UserDetailSidebar
                chat={chat}
                isOpen={showUserDetails}
                onClose={() => setShowUserDetails(false)}
                onUpdateStatus={onUpdateStatus}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Scroll to Bottom Button */}
      <AnimatePresence>
        {showScrollToBottom && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute bottom-20 right-6"
          >
            <Button
              variant="outline"
              size="sm"
              onClick={() => messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })}
              className="rounded-full shadow-lg"
            >
              ↓
            </Button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* File Drop Overlay */}
      <AnimatePresence>
        {isDraggingFile && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-blue-50/90 border-2 border-dashed border-blue-300 flex items-center justify-center z-50"
          >
            <div className="text-center">
              <PaperclipIcon className="w-12 h-12 text-blue-500 mx-auto mb-4" />
              <p className="text-lg font-medium text-blue-700">Drop files here to attach</p>
              <p className="text-sm text-blue-600">Supports PDF, images, and documents</p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Image Lightbox */}
      {lightboxImage && (
        <ImageLightbox
          imageSrc={lightboxImage}
          onClose={() => setLightboxImage(null)}
        />
      )}
    </div>
  );
};
