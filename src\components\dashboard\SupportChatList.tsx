'use client';

import React, { useEffect, useState } from 'react';
import ChatCard from '@/components/dashboard/ChatCard';
import { useRouter, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, AlertTriangle, Inbox } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChatFilters, useChatData } from '@/components/context/ChatDataContext';

export interface SupportChat {
  id: number;
  user_id: string;
  user_email: string;
  user_name: string;
  message_snippet: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'in_progress' | 'resolved';
  created_at: string;
  updated_at: string;
}

interface SupportChatListProps {
  initialFilters?: ChatFilters;
  onChatSelect?: (chat: SupportChat) => void;
}

const SupportChatList = ({ initialFilters = {}, onChatSelect }: SupportChatListProps) => {
  const router = useRouter();
  const pathname = usePathname();
  
  // Use ChatDataContext instead of local state
  const { 
    chats, 
    loadingChats, 
    chatsError, 
    fetchChats 
  } = useChatData();
  
  const [filters, setFilters] = useState<ChatFilters>({
    ...initialFilters,
    sort_by: initialFilters.sort_by || 'latest'
  });
  // Fetch chats when filters change using the context
  useEffect(() => {
    fetchChats(filters);
  }, [filters, fetchChats]);  // Listen for chat status updates from other components
  useEffect(() => {
    const handleStatusUpdate = () => {
      // Make sure we don't return true here which would indicate async response
      fetchChats(filters, true); // Force refresh
      // Return undefined explicitly (same as no return)
    };
    
    window.addEventListener('chat-status-updated', handleStatusUpdate);
    
    return () => {
      window.removeEventListener('chat-status-updated', handleStatusUpdate);
    };
  }, [filters, fetchChats]);

  // Update URL with filters for bookmarking and sharing
  const updateUrlWithFilters = (updatedFilters: ChatFilters) => {
    const params = new URLSearchParams();
    if (updatedFilters.status) params.set('status', updatedFilters.status);
    if (updatedFilters.priority) params.set('priority', updatedFilters.priority);
    if (updatedFilters.assigned_agent) params.set('agent', updatedFilters.assigned_agent);
    if (updatedFilters.sort_by) params.set('sort', updatedFilters.sort_by);
    
    router.push(`${pathname}?${params.toString()}`, { scroll: false });
  };

  const handleFilterChange = (newFilters: ChatFilters) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    updateUrlWithFilters(updatedFilters);
  };

  const handleRefresh = () => {
    fetchChats(filters, true); // Force refresh
  };

  // Animation variants for list items
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  };
  
  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { type: "spring", stiffness: 300, damping: 24 } }
  };
  return (
    <div className="flex flex-col space-y-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 p-6 rounded-lg bg-[#f8f9fa] border border-[#e0e0e0]">
        <div>
          <div className="flex items-center gap-3 flex-wrap">            
            {filters.priority && (
              <Badge variant="outline" className="bg-[#febd49]/10 text-[#113158] border-[#febd49]/30 px-4 py-2 text-sm">
                <AlertTriangle className="h-4 w-4 mr-2 text-[#febd49]" />
                Priorità {filters.priority}
              </Badge>
            )}
          </div>
            <h3 className="text-lg font-medium mt-4 text-[#113158]">
            {loadingChats ? 'Caricamento chat...' : `${chats.length} ${chats.length === 1 ? 'chat trovata' : 'chat trovate'}`}
          </h3>
        </div>        <Button
          onClick={handleRefresh}
          variant="outline"
          disabled={loadingChats}
          className="h-11 px-5 gap-2 border-[#febd49]/30 hover:bg-[#febd49]/10 hover:text-[#113158]"
        >
          {loadingChats ? 
            <div className="w-4 h-4 border-2 border-t-transparent border-[#113158] rounded-full animate-spin"></div> : 
            <RefreshCw className="h-4 w-4" />
          }
          {loadingChats ? 'Caricamento...' : 'Aggiorna'}
        </Button>
      </div>      <AnimatePresence mode="wait">
        {loadingChats ? (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="min-h-[300px] flex justify-center items-center p-8"
          >
            <div className="flex flex-col items-center">
              <div className="relative w-16 h-16">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="h-16 w-16 rounded-full border-4 border-t-[#febd49] border-x-[#113158]/30 border-b-[#113158]/30 animate-spin"></div>
                </div>
              </div>
              <p className="mt-4 text-[#113158]/70">Caricamento chat...</p>
            </div>
          </motion.div>
        ) : chatsError ? (
          <motion.div
            key="error"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="bg-red-50 border border-red-200 p-8 my-4 text-red-600 rounded-lg flex flex-col items-center justify-center min-h-[300px]"
          >
            <AlertTriangle className="w-12 h-12 mb-4 text-red-500 opacity-70" />
            <p className="text-lg font-medium">{chatsError}</p>
            <Button 
              variant="outline"
              className="mt-4 border-red-300 hover:bg-red-100 text-red-700" 
              onClick={handleRefresh}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Riprova
            </Button>
          </motion.div>
        ) : chats.length === 0 ? (
          <motion.div
            key="empty"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="bg-white rounded-lg p-16 text-center shadow-sm border flex flex-col items-center min-h-[300px] justify-center"
          >
            <div className="p-4 rounded-full bg-[#f9f9f9] mb-4">
              <Inbox className="h-10 w-10 text-[#113158]/50" />
            </div>
            <p className="text-xl font-medium text-[#113158] mb-2">Nessuna chat trovata</p>
            <p className="text-gray-500 max-w-md">
              {filters.status || filters.priority 
                ? "Prova a rimuovere alcuni filtri per vedere più risultati" 
                : "Al momento non ci sono chat di supporto disponibili"}
            </p>
            {(filters.status || filters.priority) && (
              <Button 
                variant="outline" 
                onClick={() => handleFilterChange({ status: undefined, priority: undefined })}
                className="mt-4 border-[#febd49]/30 hover:bg-[#febd49]/10 text-[#113158]"
              >
                Cancella Filtri
              </Button>
            )}
          </motion.div>
        ) : (          <motion.div 
            key="chatlist"
            variants={container}
            initial="hidden"
            animate="show"
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-6"
          >
            {chats.map(chat => (
              <motion.div key={chat.id} variants={item} className="min-h-[140px]">
                <ChatCard 
                  chat={chat} 
                  onClick={() => {
                    if (onChatSelect) {
                      onChatSelect(chat);
                    } else {
                      router.push(`/dashboard/chat/${chat.id}`);
                    }
                  }} 
                />
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SupportChatList;
